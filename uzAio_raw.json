{"danMu": [{"name": "可选线路弹幕扩展", "version": 5, "remark": "v1.6.60 及以上版本可用", "env": "弹幕线路##格式 线路名称1@地址1;线路名称2@地址2", "codeID": "4PqcO6xPFunsneXlUVMvH0iJuMLDUGrR", "order": "A01", "api": "https://raw.githubusercontent.com/woleigedouer/uzVideo-extensions/main/danMu/js/danMu4.txt", "type": 400}, {"name": "弹幕扩展", "version": 11, "remark": "搜不到就算了，不要那么执着", "env": "弹幕解析地址##内置 4 条线路。格式 地址1;地址2", "codeID": "4Y6DDMATfbw0Rg0Y2AOGpfmpU2cgspFf", "order": "A02", "api": "https://raw.githubusercontent.com/woleigedouer/uzVideo-extensions/main/danMu/js/danMu2.txt", "type": 400}, {"name": "弹幕扩展", "version": 1, "order": "A03", "api": "https://raw.githubusercontent.com/woleigedouer/uzVideo-extensions/main/danMu/js/danMu.js", "type": 400}], "panTools": [{"name": "夸克|UC|天翼|123|解析 网盘解析工具", "version": 23, "remark": "iOS14 以上版本可用,App v1.6.54 及以上版本可用", "env": "UCCookie##用于播放UC网盘视频&&UC_UT##播放视频自动获取，不可用时点击删除重新获取 cookie ，再重启app&&夸克Cookie##用于播放Quark网盘视频&&转存文件夹名称##在各网盘转存文件时使用的文件夹名称&&123网盘账号##用于播放123网盘视频&&123网盘密码##用于播放123网盘视频&&天翼网盘账号##用于播放天翼网盘视频&&天翼网盘密码##用于播放天翼网盘视频&&采集解析地址##内置两个，失效不要反馈。格式：名称1@地址1;名称2@地址2", "order": "A", "api": "https://raw.githubusercontent.com/woleigedouer/uzVideo-extensions/main/panTools/js/panTools2.js", "type": 300}, {"name": "夸克|UC|天翼|123|解析 网盘解析工具", "version": 3, "remark": "iOS15 以下版本使用", "env": "UCCookie##用于播放UC网盘视频&&UC_UT##播放视频自动获取，不可用时点击删除重新获取 cookie ，再重启app&&夸克Cookie##用于播放Quark网盘视频&&转存文件夹名称##在各网盘转存文件时使用的文件夹名称&&123网盘账号##用于播放123网盘视频&&123网盘密码##用于播放123网盘视频&&天翼网盘账号##用于播放天翼网盘视频&&天翼网盘密码##用于播放天翼网盘视频&&采集解析地址##内置两个，失效不要反馈。格式：名称1@地址1;名称2@地址2", "order": "B", "api": "https://raw.githubusercontent.com/woleigedouer/uzVideo-extensions/main/panTools/js/panTools_iOS14.js", "type": 300}], "recommend": [{"name": "豆瓣推荐", "version": 4, "remark": "iOS14 以上版本可用", "codeID": "hZC5h9Id3nP6mwaolSXqNemBLQCMFqhw", "api": "https://raw.githubusercontent.com/woleigedouer/uzVideo-extensions/main/recommend/js/douban.txt", "type": 200}], "vod": [{"name": "[解] 小猫咪", "version": 5, "remark": "通用官采解析1，需配合网盘解析工具使用。每次消耗所有解析次数！", "webSite": "https://zy.xmm.hk/api.php/provide/vod", "order": "A", "api": "https://raw.githubusercontent.com/woleigedouer/uzVideo-extensions/main/vod/js/cms.js", "type": 101}, {"name": "[解] 小猫咪", "version": 3, "remark": "通用官采解析2，需配合网盘解析工具使用。在环境变量配置 采集解析地址。", "env": "采集解析地址##内置两个，失效不要反馈。格式：名称1@地址1;名称2@地址2", "webSite": "https://zy.xmm.hk/api.php/provide/vod", "order": "A", "api": "https://raw.githubusercontent.com/woleigedouer/uzVideo-extensions/main/vod/js/cms2.js", "type": 101}, {"name": "[盘] 二小", "version": 1, "webSite": "https://2xiaopan.fun", "order": "B", "api": "https://raw.githubusercontent.com/woleigedouer/uzVideo-extensions/main/vod/js/erxiao.js", "type": 101}, {"name": "[盘] 南风", "version": 1, "webSite": "https://www.nanf.cc", "order": "B", "api": "https://raw.githubusercontent.com/woleigedouer/uzVideo-extensions/main/vod/js/nanfdj.js", "type": 101}, {"name": "[盘] 多多", "version": 3, "webSite": "https://tv.yydsys.top", "order": "B", "api": "https://raw.githubusercontent.com/woleigedouer/uzVideo-extensions/main/vod/js/duoduo.js", "type": 101}, {"name": "[盘] 木偶", "version": 2, "webSite": "https://666.666291.xyz", "order": "B", "api": "https://raw.githubusercontent.com/woleigedouer/uzVideo-extensions/main/vod/js/mogg.js", "type": 101}, {"name": "[盘] 校长", "version": 1, "remark": "🍃豆儿出品，不属精品！", "webSite": "https://xzys.fun", "codeID": "7T4PFJobJZegoH1hLUyEL1HOGSoNuwR6", "order": "B", "api": "https://raw.githubusercontent.com/woleigedouer/uzVideo-extensions/main/vod/js/Pan_xzys.txt", "type": 101}, {"name": "[盘] 欧歌", "version": 3, "webSite": "https://woog.nxog.eu.org", "order": "B", "api": "https://raw.githubusercontent.com/woleigedouer/uzVideo-extensions/main/vod/js/ouge.js", "type": 101}, {"name": "[盘] 玩偶", "version": 3, "webSite": "http://wogg.xxooo.cf", "order": "B", "api": "https://raw.githubusercontent.com/woleigedouer/uzVideo-extensions/main/vod/js/wobg.js", "type": 101}, {"name": "[盘] 至臻", "version": 4, "webSite": "http://www.miqk.cc", "order": "B", "api": "https://raw.githubusercontent.com/woleigedouer/uzVideo-extensions/main/vod/js/zhizhen.js", "type": 101}, {"name": "[盘] 茉莉", "version": 2, "remark": "🍃豆儿出品，不属精品！支持在线播放和网盘", "webSite": "https://www.hdmoli.pro/", "codeID": "OAxUOePWsN3I7NQfNIO9Il1I1cgolbSa", "order": "B", "api": "https://raw.githubusercontent.com/woleigedouer/uzVideo-extensions/main/vod/js/<PERSON>ong_<PERSON>mo<PERSON>.txt", "type": 101}, {"name": "[盘] 虎斑", "version": 1, "webSite": "http://**************:20720", "order": "B", "api": "https://raw.githubusercontent.com/woleigedouer/uzVideo-extensions/main/vod/js/huban.js", "type": 101}, {"name": "[盘] 蜡笔", "version": 4, "webSite": "http://feimaoai.site", "order": "B", "api": "https://raw.githubusercontent.com/woleigedouer/uzVideo-extensions/main/vod/js/labipan.js", "type": 101}, {"name": "[盘] 趣盘", "version": 3, "remark": "来自proversion2024，仅搜索", "webSite": "https://pan.funletu.com", "codeID": "LO5aQSJYBZLrRD1g", "instance": "funletu20241124", "order": "B", "api": "https://raw.githubusercontent.com/woleigedouer/uzVideo-extensions/main/vod/js/funletu.txt", "type": 100}, {"name": "[盘] 逸搜", "version": 3, "remark": "来自proversion2024，仅搜索", "webSite": "https://www.tianyiso.com", "codeID": "DU9z425w42hk9B8l", "instance": "tianyiso20241129", "order": "B", "api": "https://raw.githubusercontent.com/woleigedouer/uzVideo-extensions/main/vod/js/tianyiso.txt", "type": 100}, {"name": "[盘] 闪电", "version": 1, "webSite": "http://***********/", "order": "B", "api": "https://raw.githubusercontent.com/woleigedouer/uzVideo-extensions/main/vod/js/shandian.js", "type": 101}, {"name": "[盘] 雷鲸", "version": 4, "remark": "来自proversion2024", "webSite": "https://www.leijing.xyz", "codeID": "GgryhjYVgQMDUw4f", "instance": "leiJing20241126", "order": "B", "api": "https://raw.githubusercontent.com/woleigedouer/uzVideo-extensions/main/vod/js/leijing.txt", "type": 100}, {"name": "[盘] TG搜", "version": 6, "remark": "格式 频道名称1@频道id1&频道名称2@频道id2", "env": "TG搜代理地址##默认直接访问 https://t.me/s/ 有自己的代理服务填入即可，没有不用改动。", "webSite": "123资源@zyfb123&天翼日更@tianyirigeng&天翼臻影@tyysypzypd&云巢@peccxinpd&夸克UC@ucquark&夸克电影@alyp_4K_Movies&夸克剧集@alyp_TV&夸克动漫@alyp_Animation", "order": "B", "api": "https://raw.githubusercontent.com/woleigedouer/uzVideo-extensions/main/vod/js/tgs.js", "type": 101}, {"name": "[盘] TG纯搜", "version": 5, "remark": "🍃豆儿出品，不属精品！免代理纯搜索，格式 频道名称@频道id|搜索数量&频道名称@频道id，支持自定义每频道搜索数量，默认3个", "env": "TG搜API地址##https://tgsou.252035.xyz", "webSite": "123资源@zyfb123&天翼日更@tianyirigeng&天翼臻影@tyysypzypd&云巢@peccxinpd&夸克UC@ucquark&夸克电影@alyp_4K_Movies&夸克剧集@alyp_TV&夸克动漫@alyp_Animation", "order": "B", "api": "https://raw.githubusercontent.com/woleigedouer/uzVideo-extensions/main/vod/js/tgs_api.js", "type": 101}, {"name": "[直] 修罗", "version": 2, "webSite": "https://xl01.com.de", "order": "C", "api": "https://raw.githubusercontent.com/woleigedouer/uzVideo-extensions/main/vod/js/biliys.js", "type": 101}, {"name": "[直] 欧乐", "version": 3, "webSite": "https://www.olehdtv.com", "instance": "olevod20240620", "order": "C", "api": "https://raw.githubusercontent.com/woleigedouer/uzVideo-extensions/main/vod/js/olevod.js", "type": 100}, {"name": "[直] 韩剧", "version": 2, "webSite": "https://www.hanjukankan.com", "instance": "hjkk20240624", "order": "C", "api": "https://raw.githubusercontent.com/woleigedouer/uzVideo-extensions/main/vod/js/hjkk.js", "type": 100}, {"name": "[直] 瓜子¹", "version": 3, "remark": "瓜子APP", "webSite": "https://api.gudvxty.com", "codeID": "ra5Z3wtXKshzSMK2", "instance": "gzapp20241001", "order": "C", "api": "https://raw.githubusercontent.com/woleigedouer/uzVideo-extensions/main/vod/js/gzapp.txt", "type": 100}, {"name": "[直] 瓜子²", "version": 3, "remark": "瓜子WEB", "webSite": "https://api.gudvxty.com", "instance": "gzys20240822", "order": "C", "api": "https://raw.githubusercontent.com/woleigedouer/uzVideo-extensions/main/vod/js/gzys.js", "type": 100}, {"name": "[直] 素白白", "version": 3, "webSite": "https://www.subaibai.com", "instance": "sbb20240624", "order": "C", "api": "https://raw.githubusercontent.com/woleigedouer/uzVideo-extensions/main/vod/js/subaibai.js", "type": 100}, {"name": "[直] 在线之家", "version": 3, "webSite": "https://www.zxzjhd.com", "instance": "zxzj20240620", "order": "C", "api": "https://raw.githubusercontent.com/woleigedouer/uzVideo-extensions/main/vod/js/zxzj.js", "type": 100}, {"name": "[直] 4k-av", "version": 2, "webSite": "https://4k-av.com", "instance": "www4kav20240705", "order": "C", "api": "https://raw.githubusercontent.com/woleigedouer/uzVideo-extensions/main/vod/js/4kav.js", "type": 100}, {"name": "[嗅] 立播", "version": 1, "remark": "🍃豆儿出品，不属精品！", "webSite": "https://libvio.mov/", "order": "D", "api": "https://raw.githubusercontent.com/woleigedouer/uzVideo-extensions/main/vod/js/Xiu_libvio.js", "type": 101}, {"name": "[嗅] 路漫漫", "version": 1, "webSite": "https://www.lmm97.com", "order": "D", "api": "https://raw.githubusercontent.com/woleigedouer/uzVideo-extensions/main/vod/js/Xiu_lmm.js", "type": 101}, {"name": "[嗅] MX动漫", "version": 1, "webSite": "https://www.mxdm6.com", "order": "D", "api": "https://raw.githubusercontent.com/woleigedouer/uzVideo-extensions/main/vod/js/Xiu_mxdm.js", "type": 101}, {"name": "[嗅] NT动漫", "version": 1, "webSite": "https://www.ntdm9.com", "order": "D", "api": "https://raw.githubusercontent.com/woleigedouer/uzVideo-extensions/main/vod/js/Xiu_ntdm.js", "type": 101}, {"name": "[嗅] 八号影视", "version": 1, "webSite": "https://www.bahaotv.com", "order": "D", "api": "https://raw.githubusercontent.com/woleigedouer/uzVideo-extensions/main/vod/js/<PERSON>u_bhys.js", "type": 101}, {"name": "[嗅] 樱花动漫", "version": 1, "webSite": "https://www.vdm5.com", "order": "D", "api": "https://raw.githubusercontent.com/woleigedouer/uzVideo-extensions/main/vod/js/Xiu_yhdm.js", "type": 101}, {"name": "[嗅] 风车动漫", "version": 1, "webSite": "https://www.tt776b.com", "order": "D", "api": "https://raw.githubusercontent.com/woleigedouer/uzVideo-extensions/main/vod/js/Xiu_fcdm.js", "type": 101}, {"name": "[嗅] E-ACG", "version": 1, "webSite": "https://eacg1.com", "order": "D", "api": "https://raw.githubusercontent.com/woleigedouer/uzVideo-extensions/main/vod/js/eacg.js", "type": 101}, {"name": "[嗅] E-ACG2", "version": 1, "webSite": "https://eacg1.com", "order": "D", "api": "https://raw.githubusercontent.com/woleigedouer/uzVideo-extensions/main/vod/js/eacg2.js", "type": 101}, {"name": "[嗅] <PERSON><PERSON><PERSON><PERSON>", "version": 1, "webSite": "https://www.mutean.com", "order": "D", "api": "https://raw.githubusercontent.com/woleigedouer/uzVideo-extensions/main/vod/js/<PERSON>u_MuteFun.js", "type": 101}, {"name": "[嗅] giri<PERSON><PERSON>", "version": 1, "webSite": "https://anime.girigirilove.com", "order": "D", "api": "https://raw.githubusercontent.com/woleigedouer/uzVideo-extensions/main/vod/js/aggl.js", "type": 101}, {"api": "https://json.heimuer.xyz/api.php/provide/vod/", "name": "[采] 黑木耳", "order": "E", "type": 0}, {"api": "https://mozhuazy.com/api.php/provide/vod/", "name": "[采] 魔爪4k", "order": "E", "type": 0}, {"name": "[采] 自建CMS", "version": 1, "remark": "地址输入自己搭建的 cms 采集地址，默认使用如意采集占位", "webSite": "https://cj.rycjapi.com/api.php/provide/vod", "order": "E", "api": "https://raw.githubusercontent.com/woleigedouer/uzVideo-extensions/main/vod/js/cmsUser.js", "type": 101}], "live": [{"name": "📺电视直播", "api": "https://tv.iill.top/m3u/Gather", "type": 10, "remark": "来自 YanG-1989 大佬 https://github.com/YanG-1989"}, {"name": "🎥网络直播", "api": "https://tv.iill.top/m3u/Live", "type": 10, "remark": "来自 YanG-1989 大佬 https://github.com/YanG-1989"}, {"name": "🚴‍♂️体育直播", "api": "https://tv.iill.top/m3u/Sport", "type": 10, "remark": "来自 YanG-1989 大佬 https://github.com/YanG-1989"}, {"name": "📡范明明IPv6", "api": "https://cdn.jsdelivr.net/gh/fanmingming/live@refs/heads/main/tv/m3u/ipv6.m3u", "type": 10, "remark": "来自 范明明 大佬 https://github.com/fanmingming"}, {"name": "🛰️Govin", "api": "https://cdn.jsdelivr.net/gh/Guovin/iptv-api@gd/output/result.m3u", "type": 10, "remark": "来自 <PERSON>vin 大佬 https://github.com/<PERSON>vin"}]}