name: Metadata JSON Generator

on:
  push:
  workflow_dispatch:

jobs:
  generate-metadata:
    runs-on: ubuntu-latest
    permissions:
      contents: write  # 确保 GITHUB_TOKEN 具有写入权限

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0  # 获取完整的 Git 历史，避免 push 失败

    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'

    - name: Install dependencies
      run: npm install

    - name: Generate Metadata
      run: |
        node action/parse-metadata.cjs

    - name: Commit changes
      run: |
        git config --global user.name 'GitHub Action'
        git config --global user.email '<EMAIL>'
        git add uzAio.json uzAio_raw.json av_auto.json av_raw_auto.json local.json env.json README.md
        git add panTools/panTools.json danMu/danMu.json recommend/recommend.json vod/vod.json
        git add uzAio.zip
        git commit -m '该文件自动生成，无需手动修改。' || echo 'No changes to commit'
        git push
